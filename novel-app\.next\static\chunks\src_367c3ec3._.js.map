{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/NovelSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel } from '@/lib/database';\nimport { BookOpen, RefreshCw, Upload } from 'lucide-react';\n\ninterface NovelSelectorProps {\n  selectedNovel: Novel | null;\n  onNovelSelect: (novel: Novel | null) => void;\n  disabled?: boolean;\n}\n\ninterface NovelFile {\n  filename: string;\n  parsed: boolean;\n  novel: Novel | null;\n}\n\nexport default function NovelSelector({ selectedNovel, onNovelSelect, disabled }: NovelSelectorProps) {\n  const [novels, setNovels] = useState<Novel[]>([]);\n  const [availableFiles, setAvailableFiles] = useState<NovelFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [parsing, setParsing] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadNovels();\n  }, []);\n\n  const loadNovels = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        setNovels(result.data.novels);\n        setAvailableFiles(result.data.availableFiles);\n      } else {\n        console.error('加载小说列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleParseNovel = async (filename: string, reparse = false) => {\n    setParsing(filename);\n    try {\n      const response = await fetch('/api/novels', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename, reparse }),\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        await loadNovels(); // 重新加载列表\n        alert(result.message);\n      } else {\n        alert(`解析失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('解析小说失败:', error);\n      alert('解析小说失败');\n    } finally {\n      setParsing(null);\n    }\n  };\n\n  const handleNovelSelect = (novel: Novel) => {\n    if (disabled) return;\n    onNovelSelect(novel);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <BookOpen className=\"mr-2\" size={18} />\n          选择小说\n        </h2>\n        <button\n          onClick={loadNovels}\n          disabled={loading || disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"刷新列表\"\n        >\n          <RefreshCw className={`${loading ? 'animate-spin' : ''}`} size={16} />\n        </button>\n      </div>\n\n      {loading ? (\n        <div className=\"text-center py-8 text-gray-500\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {/* 已解析的小说 */}\n          {novels.length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">已解析的小说</h3>\n              <div className=\"space-y-1\">\n                {novels.map((novel) => (\n                  <div\n                    key={novel.id}\n                    onClick={() => handleNovelSelect(novel)}\n                    className={`p-2 border rounded cursor-pointer transition-colors ${\n                      selectedNovel?.id === novel.id\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"font-medium text-gray-800 text-sm\">{novel.title}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      {novel.chapterCount || 0} 章节 • {novel.filename}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 未解析的文件 */}\n          {availableFiles.filter(file => !file.parsed).length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">未解析的文件</h3>\n              <div className=\"space-y-1\">\n                {availableFiles\n                  .filter(file => !file.parsed)\n                  .map((file) => (\n                    <div\n                      key={file.filename}\n                      className=\"p-2 border border-gray-200 rounded bg-gray-50\"\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"min-w-0 flex-1\">\n                          <div className=\"font-medium text-gray-800 text-sm truncate\">{file.filename}</div>\n                          <div className=\"text-xs text-gray-500\">未解析</div>\n                        </div>\n                        <button\n                          onClick={() => handleParseNovel(file.filename)}\n                          disabled={parsing === file.filename || disabled}\n                          className=\"flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2\"\n                        >\n                          {parsing === file.filename ? (\n                            <>\n                              <RefreshCw className=\"animate-spin mr-1\" size={12} />\n                              解析中\n                            </>\n                          ) : (\n                            <>\n                              <Upload className=\"mr-1\" size={12} />\n                              解析\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n\n          {availableFiles.length === 0 && (\n            <div className=\"text-center py-6 text-gray-500\">\n              <BookOpen className=\"mx-auto mb-2\" size={32} />\n              <p className=\"text-sm\">novels 文件夹中没有找到小说文件</p>\n              <p className=\"text-xs\">请将 .txt 或 .md 文件放入 novels 文件夹</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {selectedNovel && (\n        <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded\">\n          <div className=\"text-sm text-blue-800\">\n            <strong>已选择:</strong> {selectedNovel.title}\n          </div>\n          <div className=\"text-xs text-blue-600\">\n            {selectedNovel.chapterCount || 0} 章节\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAAA;;;AAJA;;;AAkBe,SAAS,cAAc,KAA8D;QAA9D,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAsB,GAA9D;;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAc,EAAE;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAgB;IAEtD,IAAA,0KAAS;mCAAC;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI,CAAC,MAAM;gBAC5B,kBAAkB,OAAO,IAAI,CAAC,cAAc;YAC9C,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,eAAO;YAAkB,2EAAU;QAC1D,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAQ;YAC3C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,cAAc,SAAS;gBAC7B,MAAM,OAAO,OAAO;YACtB,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU;QACd,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,6NAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBACC,SAAS;wBACT,UAAU,WAAW;wBACrB,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,gOAAS;4BAAC,WAAW,AAAC,GAAgC,OAA9B,UAAU,iBAAiB;4BAAM,MAAM;;;;;;;;;;;;;;;;;YAInE,wBACC,6LAAC;gBAAI,WAAU;0BAAiC;;;;;qCAIhD,6LAAC;gBAAI,WAAU;;oBAEZ,OAAO,MAAM,GAAG,mBACf,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,uDAIR,OAHF,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE,GAC1B,+BACA,yCACL,KAAmD,OAAhD,WAAW,kCAAkC;;0DAEjD,6LAAC;gDAAI,WAAU;0DAAqC,MAAM,KAAK;;;;;;0DAC/D,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,YAAY,IAAI;oDAAE;oDAAO,MAAM,QAAQ;;;;;;;;uCAV3C,MAAM,EAAE;;;;;;;;;;;;;;;;oBAmBtB,eAAe,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBACpD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,eACE,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAC3B,GAAG,CAAC,CAAC,qBACJ,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8C,KAAK,QAAQ;;;;;;sEAC1E,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDACC,SAAS,IAAM,iBAAiB,KAAK,QAAQ;oDAC7C,UAAU,YAAY,KAAK,QAAQ,IAAI;oDACvC,WAAU;8DAET,YAAY,KAAK,QAAQ,iBACxB;;0EACE,6LAAC,gOAAS;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;qFAIvD;;0EACE,6LAAC,mNAAM;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;uCApBxC,KAAK,QAAQ;;;;;;;;;;;;;;;;oBAgC7B,eAAe,MAAM,KAAK,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6NAAQ;gCAAC,WAAU;gCAAe,MAAM;;;;;;0CACzC,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAM9B,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAO;;;;;;4BAAa;4BAAE,cAAc,KAAK;;;;;;;kCAE5C,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,YAAY,IAAI;4BAAE;;;;;;;;;;;;;;;;;;;AAM7C;GA5KwB;KAAA", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ChapterSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel, Chapter } from '@/lib/database';\nimport { FileText, Info } from 'lucide-react';\n\ninterface ChapterSelectorProps {\n  novel: Novel | null;\n  selectedChapters: string;\n  onChaptersChange: (chapters: string) => void;\n  disabled?: boolean;\n}\n\nexport default function ChapterSelector({ \n  novel, \n  selectedChapters, \n  onChaptersChange, \n  disabled \n}: ChapterSelectorProps) {\n  const [chapters, setChapters] = useState<Chapter[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [previewChapters, setPreviewChapters] = useState<number[]>([]);\n\n  useEffect(() => {\n    if (novel) {\n      loadChapters(novel.id);\n    } else {\n      setChapters([]);\n      setPreviewChapters([]);\n    }\n  }, [novel]);\n\n  useEffect(() => {\n    // 解析章节范围并预览\n    if (selectedChapters && chapters.length > 0) {\n      const parsed = parseChapterRange(selectedChapters, chapters.length);\n      setPreviewChapters(parsed);\n    } else {\n      setPreviewChapters([]);\n    }\n  }, [selectedChapters, chapters]);\n\n  const loadChapters = async (novelId: string) => {\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/chapters?novelId=${novelId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setChapters(result.data);\n      } else {\n        console.error('加载章节列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载章节列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const parseChapterRange = (rangeStr: string, maxChapter: number): number[] => {\n    const chapters: number[] = [];\n    const parts = rangeStr.split(',').map(part => part.trim());\n    \n    for (const part of parts) {\n      if (part.includes('-')) {\n        // 范围格式 (例如: \"1-5\")\n        const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n        if (!isNaN(start) && !isNaN(end) && start <= end) {\n          for (let i = start; i <= Math.min(end, maxChapter); i++) {\n            if (i > 0 && !chapters.includes(i)) {\n              chapters.push(i);\n            }\n          }\n        }\n      } else {\n        // 单个章节\n        const chapterNum = parseInt(part);\n        if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n          chapters.push(chapterNum);\n        }\n      }\n    }\n    \n    return chapters.sort((a, b) => a - b);\n  };\n\n  const handleQuickSelect = (type: 'all' | 'first10' | 'last10') => {\n    if (disabled || chapters.length === 0) return;\n\n    let range = '';\n    switch (type) {\n      case 'all':\n        range = `1-${chapters.length}`;\n        break;\n      case 'first10':\n        range = `1-${Math.min(10, chapters.length)}`;\n        break;\n      case 'last10':\n        range = `${Math.max(1, chapters.length - 9)}-${chapters.length}`;\n        break;\n    }\n    onChaptersChange(range);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <h2 className=\"text-lg font-semibold text-gray-800 mb-3 flex items-center\">\n        <FileText className=\"mr-2\" size={18} />\n        选择章节 {novel && <span className=\"text-sm text-gray-500 ml-2\">共 {chapters.length} 章</span>}\n      </h2>\n\n      {!novel ? (\n        <div className=\"text-center py-6 text-gray-500\">\n          <FileText className=\"mx-auto mb-2\" size={32} />\n          <p className=\"text-sm\">请先选择一部小说</p>\n        </div>\n      ) : loading ? (\n        <div className=\"text-center py-6 text-gray-500 text-sm\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {/* 章节范围输入 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              章节范围\n            </label>\n            <input\n              type=\"text\"\n              value={selectedChapters}\n              onChange={(e) => onChaptersChange(e.target.value)}\n              disabled={disabled}\n              placeholder=\"例如: 1-5,7,10-12\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n            />\n            <div className=\"mt-1 flex items-center text-xs text-gray-500\">\n              <Info className=\"mr-1\" size={12} />\n              支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)\n            </div>\n          </div>\n\n          {/* 快速选择按钮 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              快速选择\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => handleQuickSelect('all')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                全部章节 (1-{chapters.length})\n              </button>\n              <button\n                onClick={() => handleQuickSelect('first10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                前10章\n              </button>\n              <button\n                onClick={() => handleQuickSelect('last10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                后10章\n              </button>\n            </div>\n          </div>\n\n          {/* 章节预览 */}\n          {previewChapters.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                将要改写的章节 ({previewChapters.length} 章)\n              </label>\n              <div className=\"max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50\">\n                <div className=\"grid grid-cols-1 gap-1 text-sm\">\n                  {previewChapters.map((chapterNum) => {\n                    const chapter = chapters.find(ch => ch.chapterNumber === chapterNum);\n                    return (\n                      <div key={chapterNum} className=\"flex items-center\">\n                        <span className=\"font-medium text-blue-600 w-12\">\n                          第{chapterNum}章\n                        </span>\n                        <span className=\"text-gray-700 truncate\">\n                          {chapter?.title || '未知标题'}\n                        </span>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 章节列表 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              所有章节 ({chapters.length} 章)\n            </label>\n            <div className=\"max-h-60 overflow-y-auto border border-gray-200 rounded-md\">\n              {chapters.map((chapter) => (\n                <div\n                  key={chapter.id}\n                  className={`p-2 border-b border-gray-100 last:border-b-0 ${\n                    previewChapters.includes(chapter.chapterNumber)\n                      ? 'bg-blue-50 border-l-4 border-l-blue-500'\n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"font-medium text-gray-800 truncate\">\n                        第{chapter.chapterNumber}章 {chapter.title}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {chapter.content.length} 字符\n                      </div>\n                    </div>\n                    {previewChapters.includes(chapter.chapterNumber) && (\n                      <div className=\"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\">\n                        已选择\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;;;AAJA;;;AAae,SAAS,gBAAgB,KAKjB;QALiB,EACtC,KAAK,EACL,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACa,GALiB;;IAMtC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAEnE,IAAA,0KAAS;qCAAC;YACR,IAAI,OAAO;gBACT,aAAa,MAAM,EAAE;YACvB,OAAO;gBACL,YAAY,EAAE;gBACd,mBAAmB,EAAE;YACvB;QACF;oCAAG;QAAC;KAAM;IAEV,IAAA,0KAAS;qCAAC;YACR,YAAY;YACZ,IAAI,oBAAoB,SAAS,MAAM,GAAG,GAAG;gBAC3C,MAAM,SAAS,kBAAkB,kBAAkB,SAAS,MAAM;gBAClE,mBAAmB;YACrB,OAAO;gBACL,mBAAmB,EAAE;YACvB;QACF;oCAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,yBAAgC,OAAR;YACtD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,UAAkB;QAC3C,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAEvD,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,mBAAmB;gBACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;gBACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;oBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;wBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;4BAClC,SAAS,IAAI,CAAC;wBAChB;oBACF;gBACF;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,aAAa,SAAS;gBAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;oBACtG,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACrC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,SAAS,MAAM,KAAK,GAAG;QAEvC,IAAI,QAAQ;QACZ,OAAQ;YACN,KAAK;gBACH,QAAQ,AAAC,KAAoB,OAAhB,SAAS,MAAM;gBAC5B;YACF,KAAK;gBACH,QAAQ,AAAC,KAAkC,OAA9B,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;gBACzC;YACF,KAAK;gBACH,QAAQ,AAAC,GAAsC,OAApC,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,IAAG,KAAmB,OAAhB,SAAS,MAAM;gBAC9D;QACJ;QACA,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC,6NAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;oBAAM;oBACjC,uBAAS,6LAAC;wBAAK,WAAU;;4BAA6B;4BAAG,SAAS,MAAM;4BAAC;;;;;;;;;;;;;YAGhF,CAAC,sBACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6NAAQ;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;uBAEvB,wBACF,6LAAC;gBAAI,WAAU;0BAAyC;;;;;qCAIxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAI;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;kCAMvC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;;4CACX;4CACU,SAAS,MAAM;4CAAC;;;;;;;kDAE3B,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAOJ,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCACpD,gBAAgB,MAAM;oCAAC;;;;;;;0CAEnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC;wCACpB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,aAAa,KAAK;wCACzD,qBACE,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC;oDAAK,WAAU;;wDAAiC;wDAC7C;wDAAW;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;8DACb,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI;;;;;;;2CALb;;;;;oCASd;;;;;;;;;;;;;;;;;kCAOR,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCACvD,SAAS,MAAM;oCAAC;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,AAAC,gDAIX,OAHC,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,IAC1C,4CACA;kDAGN,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAqC;gEAChD,QAAQ,aAAa;gEAAC;gEAAG,QAAQ,KAAK;;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,OAAO,CAAC,MAAM;gEAAC;;;;;;;;;;;;;gDAG3B,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,mBAC7C,6LAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;uCAjBzE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BjC;GA/NwB;KAAA", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成\n// My First Project:AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw ankibot:AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y Generative Language Client:AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY In The Novel :AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc chat:AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk\nconst GEMINI_API_KEY = 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc';\n// const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent';\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber } = request;\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `${chapterTitle}` : ''}\n// ${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  try {\n    const prompt = buildPrompt(request);\n\n    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.7,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192,\n        },\n        safetySettings: [\n          {\n            category: \"HARM_CATEGORY_HARASSMENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_HATE_SPEECH\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          }\n        ]\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.text();\n      console.error('Gemini API error:', errorData);\n      return {\n        rewrittenText: '',\n        success: false,\n        error: `API请求失败: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    const data = await response.json();\n\n    if (!data.candidates || data.candidates.length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '没有收到有效的响应内容',\n      };\n    }\n\n    const candidate = data.candidates[0];\n\n    if (candidate.finishReason === 'SAFETY') {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n      };\n    }\n\n    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '响应内容格式错误',\n      };\n    }\n\n    const rewrittenText = candidate.content.parts[0].text;\n\n    return {\n      rewrittenText: rewrittenText.trim(),\n      success: true,\n    };\n\n  } catch (error) {\n    console.error('Gemini API调用错误:', error);\n    return {\n      rewrittenText: '',\n      success: false,\n      error: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 批量改写多个章节（支持并发）\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number) => void,\n  concurrency: number = 10\n): Promise<Array<{ success: boolean; content: string; error?: string }>> {\n  const results: Array<{ success: boolean; content: string; error?: string }> = new Array(chapters.length);\n  let completed = 0;\n\n  // 分批处理，每批最多 concurrency 个章节\n  const batches: Array<Array<{ content: string; title: string; number: number; index: number }>> = [];\n\n  for (let i = 0; i < chapters.length; i += concurrency) {\n    const batch = chapters.slice(i, i + concurrency).map((chapter, batchIndex) => ({\n      ...chapter,\n      index: i + batchIndex\n    }));\n    batches.push(batch);\n  }\n\n  for (const batch of batches) {\n    // 并发处理当前批次\n    const batchPromises = batch.map(async (chapter) => {\n      try {\n        const result = await rewriteText({\n          originalText: chapter.content,\n          rules,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n        });\n\n        results[chapter.index] = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n        };\n\n        completed++;\n\n        // 更新进度\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return result;\n      } catch (error) {\n        results[chapter.index] = {\n          success: false,\n          content: '',\n          error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        };\n\n        completed++;\n\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return null;\n      }\n    });\n\n    // 等待当前批次完成\n    await Promise.all(batchPromises);\n\n    // 批次间添加短暂延迟\n    if (batches.indexOf(batch) < batches.length - 1) {\n      await new Promise(resolve => setTimeout(resolve, 500));\n    }\n  }\n\n  return results;\n}\n\n// 测试API连接\nexport async function testGeminiConnection(): Promise<{ success: boolean; error?: string }> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 添加自定义预设规则\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,iRAAiR;;;;;;;;;;;;;AACjR,MAAM,iBAAiB;AACvB,mHAAmH;AACnH,MAAM,iBAAiB;AAevB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,OAAO,AAAC,6CAKR,OAFA,OAAM,QAGH,OADH,eAAe,AAAC,GAAe,OAAb,gBAAiB,IAAG,SAItC,OAHG,gBAAgB,AAAC,SAAsB,OAAd,eAAc,OAAK,IAAG,eAGrC,OAAb,cAAa;AASf;AAGO,eAAe,YAAY,OAAuB;IACvD,IAAI;QACF,MAAM,SAAS,YAAY;QAE3B,MAAM,WAAW,MAAM,MAAM,AAAC,GAAwB,OAAtB,gBAAe,SAAsB,OAAf,iBAAkB;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU;oBAAC;wBACT,OAAO;4BAAC;gCACN,MAAM;4BACR;yBAAE;oBACJ;iBAAE;gBACF,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,iBAAiB;gBACnB;gBACA,gBAAgB;oBACd;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;iBACD;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO,AAAC,YAA8B,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;YAC3D;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;QAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;YACvC,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YAC1F,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QAErD,OAAO;YACL,eAAe,cAAc,IAAI;YACjC,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;YACL,eAAe;YACf,SAAS;YACT,OAAO,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3D;IACF;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA+D;QAC/D,cAAA,iEAAsB;IAEtB,MAAM,UAAwE,IAAI,MAAM,SAAS,MAAM;IACvG,IAAI,YAAY;IAEhB,4BAA4B;IAC5B,MAAM,UAA2F,EAAE;IAEnG,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,YAAa;QACrD,MAAM,QAAQ,SAAS,KAAK,CAAC,GAAG,IAAI,aAAa,GAAG,CAAC,CAAC,SAAS,aAAe,CAAC;gBAC7E,GAAG,OAAO;gBACV,OAAO,IAAI;YACb,CAAC;QACD,QAAQ,IAAI,CAAC;IACf;IAEA,KAAK,MAAM,SAAS,QAAS;QAC3B,WAAW;QACX,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;YACrC,IAAI;gBACF,MAAM,SAAS,MAAM,YAAY;oBAC/B,cAAc,QAAQ,OAAO;oBAC7B;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;gBAC/B;gBAEA,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;gBACrB;gBAEA;gBAEA,OAAO;gBACP,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS;oBACT,SAAS;oBACT,OAAO,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3D;gBAEA;gBAEA,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT;QACF;QAEA,WAAW;QACX,MAAM,QAAQ,GAAG,CAAC;QAElB,YAAY;QACZ,IAAI,QAAQ,OAAO,CAAC,SAAS,QAAQ,MAAM,GAAG,GAAG;YAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,OAAO;AACT;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;QACzB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,AAAC,WAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC7D;IACF;AACF;AAGO,IAAI,eAAe;IACxB,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,AAAC,UAAoB,OAAX,KAAK,GAAG;IAC9B,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RuleEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Settings, Wand2, Save } from 'lucide-react';\nimport { PRESET_RULES } from '@/lib/gemini';\n\ninterface RuleEditorProps {\n  rules: string;\n  onRulesChange: (rules: string) => void;\n  disabled?: boolean;\n  onSaveToPreset?: (rules: string) => void;\n}\n\nexport default function RuleEditor({ rules, onRulesChange, disabled, onSaveToPreset }: RuleEditorProps) {\n  const [showPresets, setShowPresets] = useState(false);\n\n  const handlePresetSelect = (presetKey: string) => {\n    const preset = PRESET_RULES[presetKey as keyof typeof PRESET_RULES];\n    if (preset) {\n      onRulesChange(preset.rules);\n      setShowPresets(false);\n    }\n  };\n\n  const handleSaveToPreset = () => {\n    if (rules.trim() && onSaveToPreset) {\n      onSaveToPreset(rules);\n    }\n  };\n\n  const presetButtons = Object.entries(PRESET_RULES).filter(([key]) => key !== 'custom');\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n          <Settings className=\"mr-2\" size={20} />\n          改写规则\n        </h2>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={handleSaveToPreset}\n            disabled={disabled || !rules.trim()}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"保存为预设\"\n          >\n            <Save size={16} />\n          </button>\n          <button\n            onClick={() => setShowPresets(!showPresets)}\n            disabled={disabled}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"预设规则\"\n          >\n            <Wand2 size={16} />\n          </button>\n        </div>\n      </div>\n\n\n\n      {/* 预设规则 */}\n      {showPresets && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h3 className=\"font-medium text-gray-800 mb-2 text-sm\">选择预设规则</h3>\n          <div className=\"grid grid-cols-1 gap-1\">\n            {presetButtons.map(([key, preset]) => (\n              <button\n                key={key}\n                onClick={() => handlePresetSelect(key)}\n                disabled={disabled}\n                className=\"text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors\"\n              >\n                <div className=\"font-medium text-gray-800 text-sm\">{preset.name}</div>\n                <div className=\"text-xs text-gray-600\">{preset.description}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 规则编辑器 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          改写规则内容\n        </label>\n        <textarea\n          value={rules}\n          onChange={(e) => onRulesChange(e.target.value)}\n          disabled={disabled}\n          placeholder=\"请输入详细的改写规则，例如：&#10;&#10;1. 扩写男女主角之间的互动情节&#10;2. 对战斗场面一笔带过&#10;3. 增加情感描写和心理活动&#10;4. 修改不合理的人物行为&#10;...\"\n          className=\"w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100\"\n        />\n        <div className=\"mt-2 text-xs text-gray-500\">\n          {rules.length} 字符 • 建议详细描述改写要求以获得更好的效果\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAae,SAAS,WAAW,KAAmE;QAAnE,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAmB,GAAnE;;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,uIAAY,CAAC,UAAuC;QACnE,IAAI,QAAQ;YACV,cAAc,OAAO,KAAK;YAC1B,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,MAAM,IAAI,MAAM,gBAAgB;YAClC,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,OAAO,OAAO,CAAC,uIAAY,EAAE,MAAM,CAAC;YAAC,CAAC,IAAI;eAAK,QAAQ;;IAE7E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,yNAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,YAAY,CAAC,MAAM,IAAI;gCACjC,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,6MAAI;oCAAC,MAAM;;;;;;;;;;;0CAEd,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,2NAAK;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAQlB,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC;gCAAC,CAAC,KAAK,OAAO;iDAC/B,6LAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAqC,OAAO,IAAI;;;;;;kDAC/D,6LAAC;wCAAI,WAAU;kDAAyB,OAAO,WAAW;;;;;;;+BANrD;;;;;;;;;;;;;;;;;0BAcf,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAOxB;GAxFwB;KAAA", "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Users, Plus, X } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  name: string;\n  type: '男主' | '女主' | '配角' | '反派' | '其他';\n  description: string;\n}\n\ninterface CharacterManagerProps {\n  characters: Character[];\n  onCharactersChange: (characters: Character[]) => void;\n  disabled?: boolean;\n}\n\nexport default function CharacterManager({ characters, onCharactersChange, disabled }: CharacterManagerProps) {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newCharacter, setNewCharacter] = useState<Omit<Character, 'id'>>({\n    name: '',\n    type: '其他',\n    description: ''\n  });\n\n  const handleAddCharacter = () => {\n    if (!newCharacter.name.trim()) return;\n    \n    const character: Character = {\n      id: Date.now().toString(),\n      ...newCharacter\n    };\n    \n    onCharactersChange([...characters, character]);\n    setNewCharacter({ name: '', type: '其他', description: '' });\n    setShowAddForm(false);\n  };\n\n  const handleRemoveCharacter = (id: string) => {\n    onCharactersChange(characters.filter(char => char.id !== id));\n  };\n\n  const characterTypes: Character['type'][] = ['男主', '女主', '配角', '反派', '其他'];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <Users className=\"mr-2\" size={18} />\n          人物设定\n        </h2>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"添加人物\"\n        >\n          <Plus size={16} />\n        </button>\n      </div>\n\n      {/* 添加人物表单 */}\n      {showAddForm && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <div className=\"space-y-2\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                placeholder=\"人物名称\"\n                value={newCharacter.name}\n                onChange={(e) => setNewCharacter({ ...newCharacter, name: e.target.value })}\n                className=\"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              />\n              <select\n                value={newCharacter.type}\n                onChange={(e) => setNewCharacter({ ...newCharacter, type: e.target.value as Character['type'] })}\n                className=\"px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              >\n                {characterTypes.map(type => (\n                  <option key={type} value={type}>{type}</option>\n                ))}\n              </select>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"备注描述\"\n              value={newCharacter.description}\n              onChange={(e) => setNewCharacter({ ...newCharacter, description: e.target.value })}\n              className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n            />\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={handleAddCharacter}\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                添加\n              </button>\n              <button\n                onClick={() => setShowAddForm(false)}\n                className=\"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600\"\n              >\n                取消\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 人物列表 */}\n      <div className=\"space-y-2\">\n        {characters.length === 0 ? (\n          <div className=\"text-center py-4 text-gray-500 text-sm\">\n            暂无人物设定\n          </div>\n        ) : (\n          characters.map((character) => (\n            <div key={character.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"font-medium text-gray-800 text-sm\">{character.name}</span>\n                  <span className={`px-2 py-0.5 text-xs rounded ${\n                    character.type === '男主' ? 'bg-blue-100 text-blue-800' :\n                    character.type === '女主' ? 'bg-pink-100 text-pink-800' :\n                    character.type === '配角' ? 'bg-green-100 text-green-800' :\n                    character.type === '反派' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {character.type}\n                  </span>\n                </div>\n                {character.description && (\n                  <div className=\"text-xs text-gray-600 mt-1 truncate\">\n                    {character.description}\n                  </div>\n                )}\n              </div>\n              <button\n                onClick={() => handleRemoveCharacter(character.id)}\n                disabled={disabled}\n                className=\"p-1 text-gray-400 hover:text-red-600 disabled:opacity-50\"\n                title=\"删除\"\n              >\n                <X size={14} />\n              </button>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAkBe,SAAS,iBAAiB,KAAmE;QAAnE,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAyB,GAAnE;;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAwB;QACtE,MAAM;QACN,MAAM;QACN,aAAa;IACf;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,IAAI;QAE/B,MAAM,YAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,GAAG,YAAY;QACjB;QAEA,mBAAmB;eAAI;YAAY;SAAU;QAC7C,gBAAgB;YAAE,MAAM;YAAI,MAAM;YAAM,aAAa;QAAG;QACxD,eAAe;IACjB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC3D;IAEA,MAAM,iBAAsC;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAE1E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,gNAAK;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGtC,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU;wBACV,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,6MAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAKf,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;;;;;;8CAEZ,6LAAC;oCACC,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAsB;oCAC9F,WAAU;8CAET,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;4CAAkB,OAAO;sDAAO;2CAApB;;;;;;;;;;;;;;;;sCAInB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,aAAa,WAAW;4BAC/B,UAAU,CAAC,IAAM,gBAAgB;oCAAE,GAAG,YAAY;oCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAChF,WAAU;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;8BAAyC;;;;;2BAIxD,WAAW,GAAG,CAAC,CAAC,0BACd,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAqC,UAAU,IAAI;;;;;;0DACnE,6LAAC;gDAAK,WAAW,AAAC,+BAMjB,OALC,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,gCAC1B,UAAU,IAAI,KAAK,OAAO,4BAC1B;0DAEC,UAAU,IAAI;;;;;;;;;;;;oCAGlB,UAAU,WAAW,kBACpB,6LAAC;wCAAI,WAAU;kDACZ,UAAU,WAAW;;;;;;;;;;;;0CAI5B,6LAAC;gCACC,SAAS,IAAM,sBAAsB,UAAU,EAAE;gCACjD,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,oMAAC;oCAAC,MAAM;;;;;;;;;;;;uBA1BH,UAAU,EAAE;;;;;;;;;;;;;;;;AAkClC;GArIwB;KAAA", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RewriteProgress.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';\n\ninterface RewriteProgressProps {\n  jobId: string;\n  onComplete: () => void;\n}\n\ninterface JobStatus {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport default function RewriteProgress({ jobId, onComplete }: RewriteProgressProps) {\n  const [job, setJob] = useState<JobStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const interval = setInterval(checkJobStatus, 2000); // 每2秒检查一次状态\n    checkJobStatus(); // 立即检查一次\n\n    return () => clearInterval(interval);\n  }, [jobId]);\n\n  const checkJobStatus = async () => {\n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setJob(result.data);\n        setLoading(false);\n        \n        // 如果任务完成或失败，停止轮询并通知父组件\n        if (result.data.status === 'completed' || result.data.status === 'failed') {\n          setTimeout(() => {\n            onComplete();\n          }, 2000); // 2秒后通知完成\n        }\n      } else {\n        console.error('获取任务状态失败:', result.error);\n      }\n    } catch (error) {\n      console.error('获取任务状态失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={20} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={20} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return '等待处理';\n      case 'processing':\n        return '正在改写';\n      case 'completed':\n        return '改写完成';\n      case 'failed':\n        return '改写失败';\n      default:\n        return '未知状态';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n      case 'processing':\n        return 'text-blue-600 bg-blue-50 border-blue-200';\n      case 'completed':\n        return 'text-green-600 bg-green-50 border-green-200';\n      case 'failed':\n        return 'text-red-600 bg-red-50 border-red-200';\n      default:\n        return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">获取任务状态中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!job) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4 text-red-600\">\n          <XCircle className=\"mx-auto mb-2\" size={32} />\n          <p>无法获取任务状态</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">改写进度</h3>\n      \n      {/* 状态显示 */}\n      <div className={`p-4 rounded-lg border ${getStatusColor(job.status)} mb-4`}>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            {getStatusIcon(job.status)}\n            <span className=\"ml-2 font-medium\">{getStatusText(job.status)}</span>\n          </div>\n          <span className=\"text-sm\">\n            {job.progress}%\n          </span>\n        </div>\n      </div>\n\n      {/* 进度条 */}\n      <div className=\"mb-4\">\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>进度</span>\n          <span>{job.progress}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className={`h-2 rounded-full transition-all duration-300 ${\n              job.status === 'completed'\n                ? 'bg-green-500'\n                : job.status === 'failed'\n                ? 'bg-red-500'\n                : 'bg-blue-500'\n            }`}\n            style={{ width: `${job.progress}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* 时间信息 */}\n      <div className=\"text-sm text-gray-500 space-y-1\">\n        <div>开始时间: {new Date(job.createdAt).toLocaleString()}</div>\n        <div>更新时间: {new Date(job.updatedAt).toLocaleString()}</div>\n      </div>\n\n      {/* 结果信息 */}\n      {job.result && (\n        <div className=\"mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">结果信息</h4>\n          <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">{job.result}</p>\n        </div>\n      )}\n\n      {/* 操作提示 */}\n      {job.status === 'completed' && (\n        <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\n          <div className=\"flex items-center text-green-700\">\n            <CheckCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写完成！改写后的文件已保存到 data/rewritten 目录中。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'failed' && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center text-red-700\">\n            <XCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写失败，请检查错误信息并重试。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'processing' && (\n        <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n          <div className=\"flex items-center text-blue-700\">\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2\"></div>\n            <span className=\"text-sm\">\n              正在使用 Gemini AI 改写章节，请耐心等待...\n            </span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAmBe,SAAS,gBAAgB,KAA2C;QAA3C,EAAE,KAAK,EAAE,UAAU,EAAwB,GAA3C;;IACtC,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,yKAAQ,EAAmB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;qCAAC;YACR,MAAM,WAAW,YAAY,gBAAgB,OAAO,YAAY;YAChE,kBAAkB,SAAS;YAE3B;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAwB,OAAN;YAChD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;gBAClB,WAAW;gBAEX,uBAAuB;gBACvB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;oBACzE,WAAW;wBACT;oBACF,GAAG,OAAO,UAAU;gBACtB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gNAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0NAAO;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACxC,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAGzD,6LAAC;gBAAI,WAAW,AAAC,yBAAmD,OAA3B,eAAe,IAAI,MAAM,GAAE;0BAClE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,IAAI,MAAM;8CACzB,6LAAC;oCAAK,WAAU;8CAAoB,cAAc,IAAI,MAAM;;;;;;;;;;;;sCAE9D,6LAAC;4BAAK,WAAU;;gCACb,IAAI,QAAQ;gCAAC;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,IAAI,QAAQ;oCAAC;;;;;;;;;;;;;kCAEtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAW,AAAC,gDAMX,OALC,IAAI,MAAM,KAAK,cACX,iBACA,IAAI,MAAM,KAAK,WACf,eACA;4BAEN,OAAO;gCAAE,OAAO,AAAC,GAAe,OAAb,IAAI,QAAQ,EAAC;4BAAG;;;;;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;kCAClD,6LAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;;;;;;;YAInD,IAAI,MAAM,kBACT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAE,WAAU;kCAA6C,IAAI,MAAM;;;;;;;;;;;;YAKvE,IAAI,MAAM,KAAK,6BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6OAAW;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCACpC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,0BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0NAAO;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCAChC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,8BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GA3LwB;KAAA", "debugId": null}}, {"offset": {"line": 2045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { CheckCircle, XCircle, AlertCircle, X } from 'lucide-react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'info';\n  onClose: () => void;\n  duration?: number;\n}\n\nexport default function Toast({ message, type, onClose, duration = 3000 }: ToastProps) {\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration, onClose]);\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'error':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      case 'info':\n        return <AlertCircle className=\"text-blue-500\" size={20} />;\n    }\n  };\n\n  const getBgColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'info':\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  return (\n    <div className={`fixed top-4 right-4 z-50 max-w-sm w-full ${getBgColor()} border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full duration-300`}>\n      <div className=\"flex items-start space-x-3\">\n        {getIcon()}\n        <div className=\"flex-1 text-sm text-gray-800\">\n          {message}\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-gray-600\"\n        >\n          <X size={16} />\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAYe,SAAS,MAAM,KAAuD;QAAvD,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,IAAI,EAAc,GAAvD;;IAC5B,IAAA,0KAAS;2BAAC;YACR,IAAI,WAAW,GAAG;gBAChB,MAAM,QAAQ,WAAW,SAAS;gBAClC;uCAAO,IAAM,aAAa;;YAC5B;QACF;0BAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD,KAAK;gBACH,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAwD,OAAb,cAAa;kBACvE,cAAA,6LAAC;YAAI,WAAU;;gBACZ;8BACD,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,oMAAC;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9CwB;KAAA", "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport NovelSelector from '@/components/NovelSelector';\nimport ChapterSelector from '@/components/ChapterSelector';\nimport RuleEditor from '@/components/RuleEditor';\nimport CharacterManager from '@/components/CharacterManager';\nimport RewriteProgress from '@/components/RewriteProgress';\nimport Toast from '@/components/Toast';\nimport { Novel, Chapter } from '@/lib/database';\nimport { HelpCircle } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  name: string;\n  type: '男主' | '女主' | '配角' | '反派' | '其他';\n  description: string;\n}\n\ninterface ToastState {\n  show: boolean;\n  message: string;\n  type: 'success' | 'error' | 'info';\n}\n\nexport default function Home() {\n  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);\n  const [selectedChapters, setSelectedChapters] = useState<string>('');\n  const [rewriteRules, setRewriteRules] = useState<string>('');\n  const [characters, setCharacters] = useState<Character[]>([]);\n  const [isRewriting, setIsRewriting] = useState(false);\n  const [currentJobId, setCurrentJobId] = useState<string | null>(null);\n  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' });\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info') => {\n    setToast({ show: true, message, type });\n  };\n\n  const hideToast = () => {\n    setToast({ show: false, message: '', type: 'info' });\n  };\n\n  const handleSaveToPreset = async (rules: string) => {\n    const name = prompt('请输入预设名称:');\n    if (!name) return;\n\n    const description = prompt('请输入预设描述 (可选):') || '';\n\n    try {\n      const response = await fetch('/api/presets', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name,\n          description,\n          rules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        showToast('规则已保存到预设', 'success');\n      } else {\n        showToast(`保存失败: ${result.error}`, 'error');\n      }\n    } catch (error) {\n      console.error('保存预设失败:', error);\n      showToast('保存预设失败', 'error');\n    }\n  };\n\n  const handleStartRewrite = async () => {\n    if (!selectedNovel || !selectedChapters || !rewriteRules) {\n      showToast('请完整填写所有信息', 'error');\n      return;\n    }\n\n    setIsRewriting(true);\n\n    try {\n      // 构建包含人物信息的改写规则\n      let enhancedRules = rewriteRules;\n      if (characters.length > 0) {\n        const characterInfo = characters.map(char =>\n          `${char.name}(${char.type}${char.description ? ': ' + char.description : ''})`\n        ).join('、');\n        enhancedRules = `人物设定：${characterInfo}\\n\\n${rewriteRules}`;\n      }\n\n      const response = await fetch('/api/rewrite', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          novelId: selectedNovel.id,\n          chapterRange: selectedChapters,\n          rules: enhancedRules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setCurrentJobId(result.data.jobId);\n        showToast('改写任务已开始', 'info');\n      } else {\n        showToast(`改写失败: ${result.error}`, 'error');\n        setIsRewriting(false);\n      }\n    } catch (error) {\n      console.error('改写请求失败:', error);\n      showToast('改写请求失败', 'error');\n      setIsRewriting(false);\n    }\n  };\n\n  const handleRewriteComplete = () => {\n    setIsRewriting(false);\n    setCurrentJobId(null);\n    showToast('改写完成！', 'success');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            小说改写工具\n          </h1>\n          <div className=\"flex items-center space-x-4\">\n            {/* 开始改写按钮 */}\n            <button\n              onClick={handleStartRewrite}\n              disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}\n              className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors\"\n            >\n              {isRewriting ? '改写中...' : '开始改写'}\n            </button>\n            <Link\n              href=\"/help\"\n              className=\"flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors\"\n            >\n              <HelpCircle className=\"mr-1\" size={18} />\n              帮助\n            </Link>\n          </div>\n        </div>\n\n        {/* 进度显示 */}\n        {isRewriting && currentJobId && (\n          <div className=\"mb-4\">\n            <RewriteProgress\n              jobId={currentJobId}\n              onComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          {/* 左侧：改写规则 */}\n          <div className=\"lg:col-span-1\">\n            <RuleEditor\n              rules={rewriteRules}\n              onRulesChange={setRewriteRules}\n              onSaveToPreset={handleSaveToPreset}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 中间：小说选择和人物管理 */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            <NovelSelector\n              selectedNovel={selectedNovel}\n              onNovelSelect={setSelectedNovel}\n              disabled={isRewriting}\n            />\n            <CharacterManager\n              characters={characters}\n              onCharactersChange={setCharacters}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 右侧：章节选择 */}\n          <div className=\"lg:col-span-1\">\n            <ChapterSelector\n              novel={selectedNovel}\n              selectedChapters={selectedChapters}\n              onChaptersChange={setSelectedChapters}\n              disabled={isRewriting}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Toast 通知 */}\n      {toast.show && (\n        <Toast\n          message={toast.message}\n          type={toast.type}\n          onClose={hideToast}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAXA;;;;;;;;;;AA0Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAe;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAS;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAS;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAc,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAgB;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAa;QAAE,MAAM;QAAO,SAAS;QAAI,MAAM;IAAO;IAExF,MAAM,YAAY,CAAC,SAAiB;QAClC,SAAS;YAAE,MAAM;YAAM;YAAS;QAAK;IACvC;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;YAAO,SAAS;YAAI,MAAM;QAAO;IACpD;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,OAAO,OAAO;QACpB,IAAI,CAAC,MAAM;QAEX,MAAM,cAAc,OAAO,oBAAoB;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,YAAY;YACxB,OAAO;gBACL,UAAU,AAAC,SAAqB,OAAb,OAAO,KAAK,GAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;QACtB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,cAAc;YACxD,UAAU,aAAa;YACvB;QACF;QAEA,eAAe;QAEf,IAAI;YACF,gBAAgB;YAChB,IAAI,gBAAgB;YACpB,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,OACnC,AAAC,GAAe,OAAb,KAAK,IAAI,EAAC,KAAe,OAAZ,KAAK,IAAI,EAAmD,OAAhD,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW,GAAG,IAAG,MAC5E,IAAI,CAAC;gBACP,gBAAgB,AAAC,QAA2B,OAApB,eAAc,QAAmB,OAAb;YAC9C;YAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,cAAc,EAAE;oBACzB,cAAc;oBACd,OAAO;gBACT;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,OAAO,IAAI,CAAC,KAAK;gBACjC,UAAU,WAAW;YACvB,OAAO;gBACL,UAAU,AAAC,SAAqB,OAAb,OAAO,KAAK,GAAI;gBACnC,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;YACpB,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,UAAU,SAAS;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,SAAS;wCACT,UAAU,eAAe,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;wCACjE,WAAU;kDAET,cAAc,WAAW;;;;;;kDAE5B,6LAAC,0KAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,+OAAU;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;oBAO9C,eAAe,8BACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mJAAe;4BACd,OAAO;4BACP,YAAY;;;;;;;;;;;kCAKlB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8IAAU;oCACT,OAAO;oCACP,eAAe;oCACf,gBAAgB;oCAChB,UAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAa;wCACZ,eAAe;wCACf,eAAe;wCACf,UAAU;;;;;;kDAEZ,6LAAC,oJAAgB;wCACf,YAAY;wCACZ,oBAAoB;wCACpB,UAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mJAAe;oCACd,OAAO;oCACP,kBAAkB;oCAClB,kBAAkB;oCAClB,UAAU;;;;;;;;;;;;;;;;;;;;;;;YAOjB,MAAM,IAAI,kBACT,6LAAC,yIAAK;gBACJ,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,SAAS;;;;;;;;;;;;AAKnB;GAzLwB;KAAA", "debugId": null}}]}