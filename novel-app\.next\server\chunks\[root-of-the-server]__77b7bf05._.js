module.exports = [
"[project]/.next-internal/server/app/api/presets/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/lib/gemini.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Gemini API 集成
__turbopack_context__.s([
    "PRESET_RULES",
    ()=>PRESET_RULES,
    "addCustomPreset",
    ()=>addCustomPreset,
    "rewriteChapters",
    ()=>rewriteChapters,
    "rewriteText",
    ()=>rewriteText,
    "testGeminiConnection",
    ()=>testGeminiConnection
]);
const GEMINI_API_KEY = 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc';
// const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';
// 构建改写提示词
function buildPrompt(request) {
    const { originalText, rules, chapterTitle, chapterNumber } = request;
    return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${rules}

${chapterTitle ? `章节标题：${chapterTitle}` : ''}
${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}

原文内容：
${originalText}

请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持原文的基本故事框架（除非规则要求修改）
3. 确保文字流畅自然
4. 保持角色的基本性格特征（除非规则要求修改）

请直接输出改写后的内容，不要添加任何解释或说明：`;
}
async function rewriteText(request) {
    try {
        const prompt = buildPrompt(request);
        const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [
                    {
                        parts: [
                            {
                                text: prompt
                            }
                        ]
                    }
                ],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192
                },
                safetySettings: [
                    {
                        category: "HARM_CATEGORY_HARASSMENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_HATE_SPEECH",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    }
                ]
            })
        });
        if (!response.ok) {
            const errorData = await response.text();
            console.error('Gemini API error:', errorData);
            return {
                rewrittenText: '',
                success: false,
                error: `API请求失败: ${response.status} ${response.statusText}`
            };
        }
        const data = await response.json();
        if (!data.candidates || data.candidates.length === 0) {
            return {
                rewrittenText: '',
                success: false,
                error: '没有收到有效的响应内容'
            };
        }
        const candidate = data.candidates[0];
        if (candidate.finishReason === 'SAFETY') {
            return {
                rewrittenText: '',
                success: false,
                error: '内容被安全过滤器拦截，请调整改写规则或原文内容'
            };
        }
        if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
            return {
                rewrittenText: '',
                success: false,
                error: '响应内容格式错误'
            };
        }
        const rewrittenText = candidate.content.parts[0].text;
        return {
            rewrittenText: rewrittenText.trim(),
            success: true
        };
    } catch (error) {
        console.error('Gemini API调用错误:', error);
        return {
            rewrittenText: '',
            success: false,
            error: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`
        };
    }
}
async function rewriteChapters(chapters, rules, onProgress, concurrency = 10) {
    const results = new Array(chapters.length);
    let completed = 0;
    // 分批处理，每批最多 concurrency 个章节
    const batches = [];
    for(let i = 0; i < chapters.length; i += concurrency){
        const batch = chapters.slice(i, i + concurrency).map((chapter, batchIndex)=>({
                ...chapter,
                index: i + batchIndex
            }));
        batches.push(batch);
    }
    for (const batch of batches){
        // 并发处理当前批次
        const batchPromises = batch.map(async (chapter)=>{
            try {
                const result = await rewriteText({
                    originalText: chapter.content,
                    rules,
                    chapterTitle: chapter.title,
                    chapterNumber: chapter.number
                });
                results[chapter.index] = {
                    success: result.success,
                    content: result.rewrittenText,
                    error: result.error
                };
                completed++;
                // 更新进度
                if (onProgress) {
                    onProgress(completed / chapters.length * 100, chapter.number);
                }
                return result;
            } catch (error) {
                results[chapter.index] = {
                    success: false,
                    content: '',
                    error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`
                };
                completed++;
                if (onProgress) {
                    onProgress(completed / chapters.length * 100, chapter.number);
                }
                return null;
            }
        });
        // 等待当前批次完成
        await Promise.all(batchPromises);
        // 批次间添加短暂延迟
        if (batches.indexOf(batch) < batches.length - 1) {
            await new Promise((resolve)=>setTimeout(resolve, 500));
        }
    }
    return results;
}
async function testGeminiConnection() {
    try {
        const testResult = await rewriteText({
            originalText: '这是一个测试文本。',
            rules: '保持原文不变'
        });
        return {
            success: testResult.success,
            error: testResult.error
        };
    } catch (error) {
        return {
            success: false,
            error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`
        };
    }
}
let PRESET_RULES = {
    romance_focus: {
        name: '感情戏增强',
        description: '扩写男女主互动内容，对非感情戏部分一笔带过',
        rules: `请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`
    },
    character_fix: {
        name: '人设修正',
        description: '修正主角人设和对话风格',
        rules: `请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`
    },
    toxic_content_removal: {
        name: '毒点清除',
        description: '移除送女、绿帽等毒点情节',
        rules: `请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`
    },
    pacing_improvement: {
        name: '节奏优化',
        description: '优化故事节奏，删除拖沓内容',
        rules: `请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`
    },
    custom: {
        name: '自定义规则',
        description: '用户自定义的改写规则',
        rules: ''
    }
};
function addCustomPreset(name, description, rules) {
    const key = `custom_${Date.now()}`;
    PRESET_RULES = {
        ...PRESET_RULES,
        [key]: {
            name,
            description,
            rules
        }
    };
    return key;
}
}),
"[project]/src/app/api/presets/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/gemini.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const { name, description, rules } = await request.json();
        if (!name || !rules) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '名称和规则不能为空'
            }, {
                status: 400
            });
        }
        // 添加自定义预设
        const presetKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addCustomPreset"])(name, description || '', rules);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                presetKey,
                message: '预设保存成功'
            }
        });
    } catch (error) {
        console.error('保存预设失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '保存预设失败'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__77b7bf05._.js.map