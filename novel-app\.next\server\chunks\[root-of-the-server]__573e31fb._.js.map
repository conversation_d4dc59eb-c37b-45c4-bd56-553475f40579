{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n  \n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n  \n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const newNovel: Novel = {\n      ...novel,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n  \n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n    \n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n  \n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n    \n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n  \n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n  \n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n  \n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n  \n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n  \n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n    \n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n  \n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n  \n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n  \n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n  \n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n    \n    rules[index] = { \n      ...rules[index], \n      ...updates, \n      updatedAt: new Date().toISOString() \n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n  \n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n    \n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n  \n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n  \n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n  \n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n    \n    jobs[index] = { \n      ...jobs[index], \n      ...updates, \n      updatedAt: new Date().toISOString() \n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n  \n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n    \n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AA0CA,SAAS;AACT,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU;AACxC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC1C,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,YAAY,4GAAI,CAAC,IAAI,CAAC,UAAU;AAEtC,WAAW;AACX,SAAS;IACP,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,wGAAE,CAAC,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,wGAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成\nconst GEMINI_API_KEY = 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc';\n// const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent';\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber } = request;\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `章节标题：${chapterTitle}` : ''}\n${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  try {\n    const prompt = buildPrompt(request);\n\n    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.7,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192,\n        },\n        safetySettings: [\n          {\n            category: \"HARM_CATEGORY_HARASSMENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_HATE_SPEECH\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          }\n        ]\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.text();\n      console.error('Gemini API error:', errorData);\n      return {\n        rewrittenText: '',\n        success: false,\n        error: `API请求失败: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    const data = await response.json();\n\n    if (!data.candidates || data.candidates.length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '没有收到有效的响应内容',\n      };\n    }\n\n    const candidate = data.candidates[0];\n\n    if (candidate.finishReason === 'SAFETY') {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n      };\n    }\n\n    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '响应内容格式错误',\n      };\n    }\n\n    const rewrittenText = candidate.content.parts[0].text;\n\n    return {\n      rewrittenText: rewrittenText.trim(),\n      success: true,\n    };\n\n  } catch (error) {\n    console.error('Gemini API调用错误:', error);\n    return {\n      rewrittenText: '',\n      success: false,\n      error: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 批量改写多个章节（支持并发）\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number) => void,\n  concurrency: number = 10\n): Promise<Array<{ success: boolean; content: string; error?: string }>> {\n  const results: Array<{ success: boolean; content: string; error?: string }> = new Array(chapters.length);\n  let completed = 0;\n\n  // 分批处理，每批最多 concurrency 个章节\n  const batches: Array<Array<{ content: string; title: string; number: number; index: number }>> = [];\n\n  for (let i = 0; i < chapters.length; i += concurrency) {\n    const batch = chapters.slice(i, i + concurrency).map((chapter, batchIndex) => ({\n      ...chapter,\n      index: i + batchIndex\n    }));\n    batches.push(batch);\n  }\n\n  for (const batch of batches) {\n    // 并发处理当前批次\n    const batchPromises = batch.map(async (chapter) => {\n      try {\n        const result = await rewriteText({\n          originalText: chapter.content,\n          rules,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n        });\n\n        results[chapter.index] = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n        };\n\n        completed++;\n\n        // 更新进度\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return result;\n      } catch (error) {\n        results[chapter.index] = {\n          success: false,\n          content: '',\n          error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        };\n\n        completed++;\n\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return null;\n      }\n    });\n\n    // 等待当前批次完成\n    await Promise.all(batchPromises);\n\n    // 批次间添加短暂延迟\n    if (batches.indexOf(batch) < batches.length - 1) {\n      await new Promise(resolve => setTimeout(resolve, 500));\n    }\n  }\n\n  return results;\n}\n\n// 测试API连接\nexport async function testGeminiConnection(): Promise<{ success: boolean; error?: string }> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 添加自定义预设规则\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;;;;;;AAChB,MAAM,iBAAiB;AACvB,mHAAmH;AACnH,MAAM,iBAAiB;AAevB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,OAAO,CAAC;;;AAGV,EAAE,MAAM;;AAER,EAAE,eAAe,CAAC,KAAK,EAAE,cAAc,GAAG,GAAG;AAC7C,EAAE,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,GAAG,GAAG;;;AAGjD,EAAE,aAAa;;;;;;;;wBAQS,CAAC;AACzB;AAGO,eAAe,YAAY,OAAuB;IACvD,IAAI;QACF,MAAM,SAAS,YAAY;QAE3B,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,KAAK,EAAE,gBAAgB,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU;oBAAC;wBACT,OAAO;4BAAC;gCACN,MAAM;4BACR;yBAAE;oBACJ;iBAAE;gBACF,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,iBAAiB;gBACnB;gBACA,gBAAgB;oBACd;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;iBACD;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC7D;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;QAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;YACvC,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YAC1F,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QAErD,OAAO;YACL,eAAe,cAAc,IAAI;YACjC,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;YACL,eAAe;YACf,SAAS;YACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACnE;IACF;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA+D,EAC/D,cAAsB,EAAE;IAExB,MAAM,UAAwE,IAAI,MAAM,SAAS,MAAM;IACvG,IAAI,YAAY;IAEhB,4BAA4B;IAC5B,MAAM,UAA2F,EAAE;IAEnG,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,YAAa;QACrD,MAAM,QAAQ,SAAS,KAAK,CAAC,GAAG,IAAI,aAAa,GAAG,CAAC,CAAC,SAAS,aAAe,CAAC;gBAC7E,GAAG,OAAO;gBACV,OAAO,IAAI;YACb,CAAC;QACD,QAAQ,IAAI,CAAC;IACf;IAEA,KAAK,MAAM,SAAS,QAAS;QAC3B,WAAW;QACX,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;YACrC,IAAI;gBACF,MAAM,SAAS,MAAM,YAAY;oBAC/B,cAAc,QAAQ,OAAO;oBAC7B;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;gBAC/B;gBAEA,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;gBACrB;gBAEA;gBAEA,OAAO;gBACP,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS;oBACT,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACnE;gBAEA;gBAEA,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT;QACF;QAEA,WAAW;QACX,MAAM,QAAQ,GAAG,CAAC;QAElB,YAAY;QACZ,IAAI,QAAQ,OAAO,CAAC,SAAS,QAAQ,MAAM,GAAG,GAAG;YAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,OAAO;AACT;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;QACzB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACrE;IACF;AACF;AAGO,IAAI,eAAe;IACxB,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;qBAIS,CAAC;IACpB;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;oBAIQ,CAAC;IACnB;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;kBAIM,CAAC;IACjB;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;eAIG,CAAC;IACd;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;IAClC,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/file-manager.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 文件管理工具类\nexport class FileManager {\n  private static instance: FileManager;\n  private baseDir: string;\n\n  private constructor() {\n    this.baseDir = process.cwd();\n  }\n\n  public static getInstance(): FileManager {\n    if (!FileManager.instance) {\n      FileManager.instance = new FileManager();\n    }\n    return FileManager.instance;\n  }\n\n  // 确保目录存在\n  public ensureDir(dirPath: string): void {\n    if (!fs.existsSync(dirPath)) {\n      fs.mkdirSync(dirPath, { recursive: true });\n    }\n  }\n\n  // 获取novels目录路径\n  public getNovelsDir(): string {\n    return path.join(this.baseDir, '..', 'novels');\n  }\n\n  // 获取chapters目录路径\n  public getChaptersDir(): string {\n    return path.join(this.baseDir, '..', 'chapters');\n  }\n\n  // 获取数据目录路径\n  public getDataDir(): string {\n    const dataDir = path.join(this.baseDir, 'data');\n    this.ensureDir(dataDir);\n    return dataDir;\n  }\n\n  // 获取改写结果目录路径\n  public getRewrittenDir(): string {\n    const rewrittenDir = path.join(this.getDataDir(), 'rewritten');\n    this.ensureDir(rewrittenDir);\n    return rewrittenDir;\n  }\n\n  // 获取特定小说的改写结果目录\n  public getNovelRewrittenDir(novelTitle: string): string {\n    const novelDir = path.join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 获取特定小说的章节目录\n  public getNovelChaptersDir(novelTitle: string): string {\n    const chaptersDir = this.getChaptersDir();\n    this.ensureDir(chaptersDir);\n    const novelDir = path.join(chaptersDir, this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 清理文件名中的非法字符\n  public sanitizeFilename(filename: string): string {\n    return filename.replace(/[<>:\"/\\\\|?*]/g, '_').trim();\n  }\n\n  // 读取文件内容\n  public readFile(filePath: string): string {\n    try {\n      return fs.readFileSync(filePath, 'utf-8');\n    } catch (error) {\n      console.error(`读取文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 写入文件内容\n  public writeFile(filePath: string, content: string): void {\n    try {\n      const dir = path.dirname(filePath);\n      this.ensureDir(dir);\n      fs.writeFileSync(filePath, content, 'utf-8');\n    } catch (error) {\n      console.error(`写入文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 检查文件是否存在\n  public fileExists(filePath: string): boolean {\n    return fs.existsSync(filePath);\n  }\n\n  // 获取目录中的所有文件\n  public listFiles(dirPath: string, extensions?: string[]): string[] {\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return [];\n      }\n\n      const files = fs.readdirSync(dirPath);\n      \n      if (extensions) {\n        return files.filter(file => {\n          const ext = path.extname(file).toLowerCase();\n          return extensions.includes(ext);\n        });\n      }\n\n      return files;\n    } catch (error) {\n      console.error(`读取目录失败: ${dirPath}`, error);\n      return [];\n    }\n  }\n\n  // 获取文件信息\n  public getFileStats(filePath: string): fs.Stats | null {\n    try {\n      return fs.statSync(filePath);\n    } catch (error) {\n      console.error(`获取文件信息失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 删除文件\n  public deleteFile(filePath: string): boolean {\n    try {\n      if (fs.existsSync(filePath)) {\n        fs.unlinkSync(filePath);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除文件失败: ${filePath}`, error);\n      return false;\n    }\n  }\n\n  // 删除目录\n  public deleteDir(dirPath: string): boolean {\n    try {\n      if (fs.existsSync(dirPath)) {\n        fs.rmSync(dirPath, { recursive: true, force: true });\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除目录失败: ${dirPath}`, error);\n      return false;\n    }\n  }\n\n  // 复制文件\n  public copyFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.copyFileSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 移动文件\n  public moveFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.renameSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 获取目录大小\n  public getDirSize(dirPath: string): number {\n    let totalSize = 0;\n    \n    try {\n      if (!fs.existsSync(dirPath)) {\n        return 0;\n      }\n\n      const files = fs.readdirSync(dirPath);\n      \n      for (const file of files) {\n        const filePath = path.join(dirPath, file);\n        const stats = fs.statSync(filePath);\n        \n        if (stats.isDirectory()) {\n          totalSize += this.getDirSize(filePath);\n        } else {\n          totalSize += stats.size;\n        }\n      }\n    } catch (error) {\n      console.error(`计算目录大小失败: ${dirPath}`, error);\n    }\n\n    return totalSize;\n  }\n\n  // 格式化文件大小\n  public formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n    \n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // 创建备份\n  public createBackup(filePath: string): string | null {\n    try {\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n      const ext = path.extname(filePath);\n      const baseName = path.basename(filePath, ext);\n      const dir = path.dirname(filePath);\n      \n      const backupPath = path.join(dir, `${baseName}_backup_${timestamp}${ext}`);\n      \n      if (this.copyFile(filePath, backupPath)) {\n        return backupPath;\n      }\n      \n      return null;\n    } catch (error) {\n      console.error(`创建备份失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 清理旧备份文件\n  public cleanupBackups(dirPath: string, maxBackups: number = 5): void {\n    try {\n      const files = this.listFiles(dirPath);\n      const backupFiles = files\n        .filter(file => file.includes('_backup_'))\n        .map(file => ({\n          name: file,\n          path: path.join(dirPath, file),\n          stats: this.getFileStats(path.join(dirPath, file))\n        }))\n        .filter(item => item.stats !== null)\n        .sort((a, b) => b.stats!.mtime.getTime() - a.stats!.mtime.getTime());\n\n      // 删除超出数量限制的备份文件\n      if (backupFiles.length > maxBackups) {\n        const filesToDelete = backupFiles.slice(maxBackups);\n        for (const file of filesToDelete) {\n          this.deleteFile(file.path);\n        }\n      }\n    } catch (error) {\n      console.error(`清理备份文件失败: ${dirPath}`, error);\n    }\n  }\n}\n\n// 导出单例实例\nexport const fileManager = FileManager.getInstance();\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,MAAM;IACX,OAAe,SAAsB;IAC7B,QAAgB;IAExB,aAAsB;QACpB,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG;IAC5B;IAEA,OAAc,cAA2B;QACvC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,SAAS;IACF,UAAU,OAAe,EAAQ;QACtC,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;YAC3B,wGAAE,CAAC,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;IACF;IAEA,eAAe;IACR,eAAuB;QAC5B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,iBAAiB;IACV,iBAAyB;QAC9B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,WAAW;IACJ,aAAqB;QAC1B,MAAM,UAAU,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACxC,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,aAAa;IACN,kBAA0B;QAC/B,MAAM,eAAe,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;QAClD,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,gBAAgB;IACT,qBAAqB,UAAkB,EAAU;QACtD,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,oBAAoB,UAAkB,EAAU;QACrD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,SAAS,CAAC;QACf,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,iBAAiB,QAAgB,EAAU;QAChD,OAAO,SAAS,OAAO,CAAC,iBAAiB,KAAK,IAAI;IACpD;IAEA,SAAS;IACF,SAAS,QAAgB,EAAU;QACxC,IAAI;YACF,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,SAAS;IACF,UAAU,QAAgB,EAAE,OAAe,EAAQ;QACxD,IAAI;YACF,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,aAAa,CAAC,UAAU,SAAS;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,WAAW;IACJ,WAAW,QAAgB,EAAW;QAC3C,OAAO,wGAAE,CAAC,UAAU,CAAC;IACvB;IAEA,aAAa;IACN,UAAU,OAAe,EAAE,UAAqB,EAAY;QACjE,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO,EAAE;YACX;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,IAAI,YAAY;gBACd,OAAO,MAAM,MAAM,CAAC,CAAA;oBAClB,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC,MAAM,WAAW;oBAC1C,OAAO,WAAW,QAAQ,CAAC;gBAC7B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO,EAAE;QACX;IACF;IAEA,SAAS;IACF,aAAa,QAAgB,EAAmB;QACrD,IAAI;YACF,OAAO,wGAAE,CAAC,QAAQ,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE;YACvC,OAAO;QACT;IACF;IAEA,OAAO;IACA,WAAW,QAAgB,EAAW;QAC3C,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,wGAAE,CAAC,UAAU,CAAC;gBACd,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;IACA,UAAU,OAAe,EAAW;QACzC,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC1B,wGAAE,CAAC,MAAM,CAAC,SAAS;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAClD,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,YAAY,CAAC,SAAS;YACzB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,SAAS;IACF,WAAW,OAAe,EAAU;QACzC,IAAI,YAAY;QAEhB,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO;YACT;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,KAAK,MAAM,QAAQ,MAAO;gBACxB,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,SAAS;gBACpC,MAAM,QAAQ,wGAAE,CAAC,QAAQ,CAAC;gBAE1B,IAAI,MAAM,WAAW,IAAI;oBACvB,aAAa,IAAI,CAAC,UAAU,CAAC;gBAC/B,OAAO;oBACL,aAAa,MAAM,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;QAEA,OAAO;IACT;IAEA,UAAU;IACH,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO;IACA,aAAa,QAAgB,EAAiB;QACnD,IAAI;YACF,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS;YAC5D,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC,UAAU;YACzC,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YAEzB,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,QAAQ,EAAE,YAAY,KAAK;YAEzE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,aAAa;gBACvC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,UAAU;IACH,eAAe,OAAe,EAAE,aAAqB,CAAC,EAAQ;QACnE,IAAI;YACF,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC;YAC7B,MAAM,cAAc,MACjB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,aAC7B,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACZ,MAAM;oBACN,MAAM,4GAAI,CAAC,IAAI,CAAC,SAAS;oBACzB,OAAO,IAAI,CAAC,YAAY,CAAC,4GAAI,CAAC,IAAI,CAAC,SAAS;gBAC9C,CAAC,GACA,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO,KAAK,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO;YAEnE,gBAAgB;YAChB,IAAI,YAAY,MAAM,GAAG,YAAY;gBACnC,MAAM,gBAAgB,YAAY,KAAK,CAAC;gBACxC,KAAK,MAAM,QAAQ,cAAe;oBAChC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;IACF;AACF;AAGO,MAAM,cAAc,YAAY,WAAW", "debugId": null}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/novel-parser.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { novelDb, chapterDb, Novel, Chapter } from './database';\nimport { fileManager } from './file-manager';\n\n// 小说解析配置\ninterface ParseConfig {\n  chapterPattern: RegExp;\n  minChapterLength: number;\n  maxChapterLength: number;\n}\n\n// 默认章节匹配模式\nconst DEFAULT_CHAPTER_PATTERNS = [\n  /^第[一二三四五六七八九十百千万\\d]+章.*$/gm,\n  /^第[0-9]+章.*$/gm,\n  /^Chapter\\s+\\d+.*$/gmi,\n  /^第[0-9]+节.*$/gm,\n  /^\\d+\\..*$/gm,\n];\n\n// 解析小说文件\nexport async function parseNovelFile(filePath: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n}> {\n  const filename = path.basename(filePath);\n  const title = filename.replace(/\\.(txt|md)$/i, '');\n\n  // 读取文件内容\n  const content = fs.readFileSync(filePath, 'utf-8');\n\n  // 创建小说记录\n  const novel = novelDb.create({\n    title,\n    filename,\n  });\n\n  // 解析章节\n  const chapters = parseChapters(content, novel.id);\n\n  // 批量创建章节记录\n  const createdChapters = chapterDb.createBatch(chapters);\n\n  // 更新小说的章节数量\n  novelDb.update(novel.id, { chapterCount: createdChapters.length });\n\n  // 保存章节文件\n  await saveChapterFiles(createdChapters);\n\n  return {\n    novel: { ...novel, chapterCount: createdChapters.length },\n    chapters: createdChapters,\n  };\n}\n\n// 解析章节内容\nfunction parseChapters(content: string, novelId: string): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n\n  // 尝试不同的章节匹配模式\n  let bestPattern: RegExp | null = null;\n  let bestMatches: RegExpMatchArray[] = [];\n\n  for (const pattern of DEFAULT_CHAPTER_PATTERNS) {\n    const matches = Array.from(content.matchAll(pattern));\n    console.log(`Pattern ${pattern.source} found ${matches.length} matches`);\n    if (matches.length > bestMatches.length) {\n      bestPattern = pattern;\n      bestMatches = matches;\n    }\n  }\n\n  console.log(`Best pattern found ${bestMatches.length} chapters`);\n\n  if (!bestPattern || bestMatches.length === 0) {\n    // 如果没有找到章节标记，将整个文件作为一章\n    chapters.push({\n      novelId,\n      chapterNumber: 1,\n      title: '全文',\n      content: content.trim(),\n      filename: `chapter_1.txt`,\n    });\n    return chapters;\n  }\n\n  // 根据匹配结果分割章节\n  const chapterPositions = bestMatches.map((match, index) => ({\n    index: match.index!,\n    title: extractChapterTitle(match[0]),\n    chapterNumber: index + 1,\n  }));\n\n  for (let i = 0; i < chapterPositions.length; i++) {\n    const currentPos = chapterPositions[i];\n    const nextPos = chapterPositions[i + 1];\n\n    const startIndex = currentPos.index;\n    const endIndex = nextPos ? nextPos.index : content.length;\n\n    const chapterContent = content.slice(startIndex, endIndex).trim();\n\n    if (chapterContent.length > 50) { // 过滤太短的章节\n      chapters.push({\n        novelId,\n        chapterNumber: currentPos.chapterNumber,\n        title: currentPos.title || `第${currentPos.chapterNumber}章`,\n        content: chapterContent,\n        filename: `chapter_${currentPos.chapterNumber}.txt`,\n      });\n    }\n  }\n\n  console.log(`Successfully parsed ${chapters.length} chapters`);\n  return chapters;\n}\n\n// 提取章节标题\nfunction extractChapterTitle(chapterText: string): string {\n  const lines = chapterText.split('\\n');\n  const firstLine = lines[0].trim();\n\n  // 如果第一行看起来像标题，使用它\n  if (firstLine.length < 100 && firstLine.length > 0) {\n    return firstLine;\n  }\n\n  // 否则尝试从前几行中找到标题\n  for (let i = 0; i < Math.min(3, lines.length); i++) {\n    const line = lines[i].trim();\n    if (line.length > 0 && line.length < 100) {\n      return line;\n    }\n  }\n\n  return '未命名章节';\n}\n\n// 保存章节文件到chapters目录\nasync function saveChapterFiles(chapters: Chapter[]): Promise<void> {\n  // 为每个小说创建子目录\n  const novelIds = [...new Set(chapters.map(ch => ch.novelId))];\n\n  for (const novelId of novelIds) {\n    const novel = novelDb.getById(novelId);\n    if (!novel) continue;\n\n    const novelDir = fileManager.getNovelChaptersDir(novel.title);\n\n    // 保存该小说的所有章节\n    const novelChapters = chapters.filter(ch => ch.novelId === novelId);\n    for (const chapter of novelChapters) {\n      const chapterPath = path.join(novelDir, chapter.filename);\n      fileManager.writeFile(chapterPath, chapter.content);\n    }\n  }\n}\n\n// 获取novels目录中的所有小说文件\nexport function getAvailableNovels(): string[] {\n  const novelsDir = fileManager.getNovelsDir();\n  return fileManager.listFiles(novelsDir, ['.txt', '.md']);\n}\n\n// 检查小说是否已经被解析\nexport function isNovelParsed(filename: string): boolean {\n  const novels = novelDb.getAll();\n  return novels.some(novel => novel.filename === filename);\n}\n\n// 重新解析小说（删除旧数据并重新解析）\nexport async function reparseNovel(filename: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n} | null> {\n  const novelsDir = fileManager.getNovelsDir();\n  const filePath = path.join(novelsDir, filename);\n\n  if (!fileManager.fileExists(filePath)) {\n    return null;\n  }\n\n  // 删除旧的小说和章节数据\n  const existingNovels = novelDb.getAll();\n  const existingNovel = existingNovels.find(novel => novel.filename === filename);\n\n  if (existingNovel) {\n    chapterDb.deleteByNovelId(existingNovel.id);\n    novelDb.delete(existingNovel.id);\n  }\n\n  // 重新解析\n  return await parseNovelFile(filePath);\n}\n\n// 解析章节范围字符串 (例如: \"1-5,7,10-12\")\nexport function parseChapterRange(rangeStr: string, maxChapter: number): number[] {\n  const chapters: number[] = [];\n  const parts = rangeStr.split(',').map(part => part.trim());\n\n  for (const part of parts) {\n    if (part.includes('-')) {\n      // 范围格式 (例如: \"1-5\")\n      const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n      if (!isNaN(start) && !isNaN(end) && start <= end) {\n        for (let i = start; i <= Math.min(end, maxChapter); i++) {\n          if (i > 0 && !chapters.includes(i)) {\n            chapters.push(i);\n          }\n        }\n      }\n    } else {\n      // 单个章节\n      const chapterNum = parseInt(part);\n      if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n        chapters.push(chapterNum);\n      }\n    }\n  }\n\n  return chapters.sort((a, b) => a - b);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AASA,WAAW;AACX,MAAM,2BAA2B;IAC/B;IACA;IACA;IACA;IACA;CACD;AAGM,eAAe,eAAe,QAAgB;IAInD,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC;IAC/B,MAAM,QAAQ,SAAS,OAAO,CAAC,gBAAgB;IAE/C,SAAS;IACT,MAAM,UAAU,wGAAE,CAAC,YAAY,CAAC,UAAU;IAE1C,SAAS;IACT,MAAM,QAAQ,mIAAO,CAAC,MAAM,CAAC;QAC3B;QACA;IACF;IAEA,OAAO;IACP,MAAM,WAAW,cAAc,SAAS,MAAM,EAAE;IAEhD,WAAW;IACX,MAAM,kBAAkB,qIAAS,CAAC,WAAW,CAAC;IAE9C,YAAY;IACZ,mIAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;QAAE,cAAc,gBAAgB,MAAM;IAAC;IAEhE,SAAS;IACT,MAAM,iBAAiB;IAEvB,OAAO;QACL,OAAO;YAAE,GAAG,KAAK;YAAE,cAAc,gBAAgB,MAAM;QAAC;QACxD,UAAU;IACZ;AACF;AAEA,SAAS;AACT,SAAS,cAAc,OAAe,EAAE,OAAe;IACrD,MAAM,WAAgD,EAAE;IAExD,cAAc;IACd,IAAI,cAA6B;IACjC,IAAI,cAAkC,EAAE;IAExC,KAAK,MAAM,WAAW,yBAA0B;QAC9C,MAAM,UAAU,MAAM,IAAI,CAAC,QAAQ,QAAQ,CAAC;QAC5C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;QACvE,IAAI,QAAQ,MAAM,GAAG,YAAY,MAAM,EAAE;YACvC,cAAc;YACd,cAAc;QAChB;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,YAAY,MAAM,CAAC,SAAS,CAAC;IAE/D,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;QAC5C,uBAAuB;QACvB,SAAS,IAAI,CAAC;YACZ;YACA,eAAe;YACf,OAAO;YACP,SAAS,QAAQ,IAAI;YACrB,UAAU,CAAC,aAAa,CAAC;QAC3B;QACA,OAAO;IACT;IAEA,aAAa;IACb,MAAM,mBAAmB,YAAY,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC1D,OAAO,MAAM,KAAK;YAClB,OAAO,oBAAoB,KAAK,CAAC,EAAE;YACnC,eAAe,QAAQ;QACzB,CAAC;IAED,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;QAChD,MAAM,aAAa,gBAAgB,CAAC,EAAE;QACtC,MAAM,UAAU,gBAAgB,CAAC,IAAI,EAAE;QAEvC,MAAM,aAAa,WAAW,KAAK;QACnC,MAAM,WAAW,UAAU,QAAQ,KAAK,GAAG,QAAQ,MAAM;QAEzD,MAAM,iBAAiB,QAAQ,KAAK,CAAC,YAAY,UAAU,IAAI;QAE/D,IAAI,eAAe,MAAM,GAAG,IAAI;YAC9B,SAAS,IAAI,CAAC;gBACZ;gBACA,eAAe,WAAW,aAAa;gBACvC,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,EAAE,WAAW,aAAa,CAAC,CAAC,CAAC;gBAC1D,SAAS;gBACT,UAAU,CAAC,QAAQ,EAAE,WAAW,aAAa,CAAC,IAAI,CAAC;YACrD;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;IAC7D,OAAO;AACT;AAEA,SAAS;AACT,SAAS,oBAAoB,WAAmB;IAC9C,MAAM,QAAQ,YAAY,KAAK,CAAC;IAChC,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,IAAI;IAE/B,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,OAAO,UAAU,MAAM,GAAG,GAAG;QAClD,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;QAClD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAC1B,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK;YACxC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,oBAAoB;AACpB,eAAe,iBAAiB,QAAmB;IACjD,aAAa;IACb,MAAM,WAAW;WAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,KAAM,GAAG,OAAO;KAAG;IAE7D,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,WAAW,8IAAW,CAAC,mBAAmB,CAAC,MAAM,KAAK;QAE5D,aAAa;QACb,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK;QAC3D,KAAK,MAAM,WAAW,cAAe;YACnC,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU,QAAQ,QAAQ;YACxD,8IAAW,CAAC,SAAS,CAAC,aAAa,QAAQ,OAAO;QACpD;IACF;AACF;AAGO,SAAS;IACd,MAAM,YAAY,8IAAW,CAAC,YAAY;IAC1C,OAAO,8IAAW,CAAC,SAAS,CAAC,WAAW;QAAC;QAAQ;KAAM;AACzD;AAGO,SAAS,cAAc,QAAgB;IAC5C,MAAM,SAAS,mIAAO,CAAC,MAAM;IAC7B,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACjD;AAGO,eAAe,aAAa,QAAgB;IAIjD,MAAM,YAAY,8IAAW,CAAC,YAAY;IAC1C,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,IAAI,CAAC,8IAAW,CAAC,UAAU,CAAC,WAAW;QACrC,OAAO;IACT;IAEA,cAAc;IACd,MAAM,iBAAiB,mIAAO,CAAC,MAAM;IACrC,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAEtE,IAAI,eAAe;QACjB,qIAAS,CAAC,eAAe,CAAC,cAAc,EAAE;QAC1C,mIAAO,CAAC,MAAM,CAAC,cAAc,EAAE;IACjC;IAEA,OAAO;IACP,OAAO,MAAM,eAAe;AAC9B;AAGO,SAAS,kBAAkB,QAAgB,EAAE,UAAkB;IACpE,MAAM,WAAqB,EAAE;IAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAEvD,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,mBAAmB;YACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;YACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;gBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;oBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;wBAClC,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;QACF,OAAO;YACL,OAAO;YACP,MAAM,aAAa,SAAS;YAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;gBACtG,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;AACrC", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/api/rewrite/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { chapterDb, jobDb, novelDb } from '@/lib/database';\nimport { rewriteChapters } from '@/lib/gemini';\nimport { parseChapterRange } from '@/lib/novel-parser';\nimport { fileManager } from '@/lib/file-manager';\nimport path from 'path';\n\n// POST - 创建改写任务\nexport async function POST(request: NextRequest) {\n  try {\n    const { novelId, chapterRange, rules } = await request.json();\n\n    if (!novelId || !chapterRange || !rules) {\n      return NextResponse.json(\n        { success: false, error: '参数不完整' },\n        { status: 400 }\n      );\n    }\n\n    // 获取小说信息\n    const novel = novelDb.getById(novelId);\n    if (!novel) {\n      return NextResponse.json(\n        { success: false, error: '小说不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 获取所有章节\n    const allChapters = chapterDb.getByNovelId(novelId);\n    if (allChapters.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '该小说没有章节' },\n        { status: 404 }\n      );\n    }\n\n    // 解析章节范围\n    const chapterNumbers = parseChapterRange(chapterRange, allChapters.length);\n    if (chapterNumbers.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '无效的章节范围' },\n        { status: 400 }\n      );\n    }\n\n    // 获取要改写的章节\n    const chaptersToRewrite = allChapters.filter(chapter =>\n      chapterNumbers.includes(chapter.chapterNumber)\n    );\n\n    if (chaptersToRewrite.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '没有找到指定的章节' },\n        { status: 404 }\n      );\n    }\n\n    // 创建改写任务\n    const job = jobDb.create({\n      novelId,\n      chapters: chapterNumbers,\n      ruleId: 'custom', // 暂时使用custom，后续可以关联到规则表\n      status: 'pending',\n      progress: 0,\n    });\n\n    // 异步执行改写任务\n    executeRewriteJob(job.id, chaptersToRewrite, rules, novel.title);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        jobId: job.id,\n        chaptersCount: chaptersToRewrite.length,\n        message: '改写任务已创建，正在处理中...',\n      },\n    });\n\n  } catch (error) {\n    console.error('创建改写任务失败:', error);\n    return NextResponse.json(\n      { success: false, error: '创建改写任务失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// 异步执行改写任务\nasync function executeRewriteJob(\n  jobId: string,\n  chapters: any[],\n  rules: string,\n  novelTitle: string\n) {\n  try {\n    // 更新任务状态为处理中\n    jobDb.update(jobId, { status: 'processing', progress: 0 });\n\n    // 准备章节数据\n    const chaptersData = chapters.map(chapter => ({\n      content: chapter.content,\n      title: chapter.title,\n      number: chapter.chapterNumber,\n    }));\n\n    // 执行改写（支持10章并发）\n    const results = await rewriteChapters(\n      chaptersData,\n      rules,\n      (progress, currentChapter) => {\n        // 更新进度\n        jobDb.update(jobId, { progress: Math.round(progress) });\n      },\n      10 // 并发数量\n    );\n\n    // 保存改写结果\n    const outputDir = fileManager.getNovelRewrittenDir(novelTitle);\n\n    let successCount = 0;\n    const resultSummary: any[] = [];\n\n    for (let i = 0; i < results.length; i++) {\n      const result = results[i];\n      const chapter = chapters[i];\n\n      if (result.success) {\n        // 保存改写后的章节\n        const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;\n        const filePath = path.join(outputDir, filename);\n        fileManager.writeFile(filePath, result.content);\n        successCount++;\n      }\n\n      resultSummary.push({\n        chapterNumber: chapter.chapterNumber,\n        chapterTitle: chapter.title,\n        success: result.success,\n        error: result.error,\n      });\n    }\n\n    // 保存结果摘要\n    const summaryPath = path.join(outputDir, 'rewrite_summary.json');\n    const summaryData = JSON.stringify({\n      jobId,\n      novelTitle,\n      rules,\n      totalChapters: chapters.length,\n      successCount,\n      failedCount: chapters.length - successCount,\n      results: resultSummary,\n      completedAt: new Date().toISOString(),\n    }, null, 2);\n    fileManager.writeFile(summaryPath, summaryData);\n\n    // 更新任务状态\n    jobDb.update(jobId, {\n      status: 'completed',\n      progress: 100,\n      result: `成功改写 ${successCount}/${chapters.length} 章节，结果保存在: ${outputDir}`,\n    });\n\n  } catch (error) {\n    console.error('执行改写任务失败:', error);\n    jobDb.update(jobId, {\n      status: 'failed',\n      result: `改写失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    });\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3D,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO;YACvC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO;YACV,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,cAAc,qIAAS,CAAC,YAAY,CAAC;QAC3C,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,iBAAiB,IAAA,oJAAiB,EAAC,cAAc,YAAY,MAAM;QACzE,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,oBAAoB,YAAY,MAAM,CAAC,CAAA,UAC3C,eAAe,QAAQ,CAAC,QAAQ,aAAa;QAG/C,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,MAAM,iIAAK,CAAC,MAAM,CAAC;YACvB;YACA,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,UAAU;QACZ;QAEA,WAAW;QACX,kBAAkB,IAAI,EAAE,EAAE,mBAAmB,OAAO,MAAM,KAAK;QAE/D,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO,IAAI,EAAE;gBACb,eAAe,kBAAkB,MAAM;gBACvC,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,WAAW;AACX,eAAe,kBACb,KAAa,EACb,QAAe,EACf,KAAa,EACb,UAAkB;IAElB,IAAI;QACF,aAAa;QACb,iIAAK,CAAC,MAAM,CAAC,OAAO;YAAE,QAAQ;YAAc,UAAU;QAAE;QAExD,SAAS;QACT,MAAM,eAAe,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC5C,SAAS,QAAQ,OAAO;gBACxB,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,aAAa;YAC/B,CAAC;QAED,gBAAgB;QAChB,MAAM,UAAU,MAAM,IAAA,yIAAe,EACnC,cACA,OACA,CAAC,UAAU;YACT,OAAO;YACP,iIAAK,CAAC,MAAM,CAAC,OAAO;gBAAE,UAAU,KAAK,KAAK,CAAC;YAAU;QACvD,GACA,GAAG,OAAO;;QAGZ,SAAS;QACT,MAAM,YAAY,8IAAW,CAAC,oBAAoB,CAAC;QAEnD,IAAI,eAAe;QACnB,MAAM,gBAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,UAAU,QAAQ,CAAC,EAAE;YAE3B,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW;gBACX,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC,cAAc,CAAC;gBACjE,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;gBACtC,8IAAW,CAAC,SAAS,CAAC,UAAU,OAAO,OAAO;gBAC9C;YACF;YAEA,cAAc,IAAI,CAAC;gBACjB,eAAe,QAAQ,aAAa;gBACpC,cAAc,QAAQ,KAAK;gBAC3B,SAAS,OAAO,OAAO;gBACvB,OAAO,OAAO,KAAK;YACrB;QACF;QAEA,SAAS;QACT,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,WAAW;QACzC,MAAM,cAAc,KAAK,SAAS,CAAC;YACjC;YACA;YACA;YACA,eAAe,SAAS,MAAM;YAC9B;YACA,aAAa,SAAS,MAAM,GAAG;YAC/B,SAAS;YACT,aAAa,IAAI,OAAO,WAAW;QACrC,GAAG,MAAM;QACT,8IAAW,CAAC,SAAS,CAAC,aAAa;QAEnC,SAAS;QACT,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;QAC1E;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,QAAQ,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACpE;IACF;AACF", "debugId": null}}]}