// Gemini API 集成
// My First Project:AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw ankibot:AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y Generative Language Client:AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY In The Novel :AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc chat:AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk
const GEMINI_API_KEY = 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc';
// const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';

export interface RewriteRequest {
  originalText: string;
  rules: string;
  chapterTitle?: string;
  chapterNumber?: number;
}

export interface RewriteResponse {
  rewrittenText: string;
  success: boolean;
  error?: string;
}

// 构建改写提示词
function buildPrompt(request: RewriteRequest): string {
  const { originalText, rules, chapterTitle, chapterNumber } = request;

  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${rules}

${chapterTitle ? `${chapterTitle}` : ''}
// ${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}

原文内容：
${originalText}

请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持原文的基本故事框架（除非规则要求修改）
3. 确保文字流畅自然
4. 保持角色的基本性格特征（除非规则要求修改）

请直接输出改写后的内容，不要添加任何解释或说明：`;
}

// 调用Gemini API进行文本改写
export async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {
  try {
    const prompt = buildPrompt(request);

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Gemini API error:', errorData);
      return {
        rewrittenText: '',
        success: false,
        error: `API请求失败: ${response.status} ${response.statusText}`,
      };
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0) {
      return {
        rewrittenText: '',
        success: false,
        error: '没有收到有效的响应内容',
      };
    }

    const candidate = data.candidates[0];

    if (candidate.finishReason === 'SAFETY') {
      return {
        rewrittenText: '',
        success: false,
        error: '内容被安全过滤器拦截，请调整改写规则或原文内容',
      };
    }

    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      return {
        rewrittenText: '',
        success: false,
        error: '响应内容格式错误',
      };
    }

    const rewrittenText = candidate.content.parts[0].text;

    return {
      rewrittenText: rewrittenText.trim(),
      success: true,
    };

  } catch (error) {
    console.error('Gemini API调用错误:', error);
    return {
      rewrittenText: '',
      success: false,
      error: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 批量改写多个章节（支持并发）
export async function rewriteChapters(
  chapters: Array<{ content: string; title: string; number: number }>,
  rules: string,
  onProgress?: (progress: number, currentChapter: number) => void,
  concurrency: number = 10
): Promise<Array<{ success: boolean; content: string; error?: string }>> {
  const results: Array<{ success: boolean; content: string; error?: string }> = new Array(chapters.length);
  let completed = 0;

  // 分批处理，每批最多 concurrency 个章节
  const batches: Array<Array<{ content: string; title: string; number: number; index: number }>> = [];

  for (let i = 0; i < chapters.length; i += concurrency) {
    const batch = chapters.slice(i, i + concurrency).map((chapter, batchIndex) => ({
      ...chapter,
      index: i + batchIndex
    }));
    batches.push(batch);
  }

  for (const batch of batches) {
    // 并发处理当前批次
    const batchPromises = batch.map(async (chapter) => {
      try {
        const result = await rewriteText({
          originalText: chapter.content,
          rules,
          chapterTitle: chapter.title,
          chapterNumber: chapter.number,
        });

        results[chapter.index] = {
          success: result.success,
          content: result.rewrittenText,
          error: result.error,
        };

        completed++;

        // 更新进度
        if (onProgress) {
          onProgress((completed / chapters.length) * 100, chapter.number);
        }

        return result;
      } catch (error) {
        results[chapter.index] = {
          success: false,
          content: '',
          error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };

        completed++;

        if (onProgress) {
          onProgress((completed / chapters.length) * 100, chapter.number);
        }

        return null;
      }
    });

    // 等待当前批次完成
    await Promise.all(batchPromises);

    // 批次间添加短暂延迟
    if (batches.indexOf(batch) < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  return results;
}

// 测试API连接
export async function testGeminiConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    const testResult = await rewriteText({
      originalText: '这是一个测试文本。',
      rules: '保持原文不变',
    });

    return {
      success: testResult.success,
      error: testResult.error,
    };
  } catch (error) {
    return {
      success: false,
      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 预设的改写规则模板
export let PRESET_RULES = {
  romance_focus: {
    name: '感情戏增强',
    description: '扩写男女主互动内容，对非感情戏部分一笔带过',
    rules: `请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`
  },

  character_fix: {
    name: '人设修正',
    description: '修正主角人设和对话风格',
    rules: `请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`
  },

  toxic_content_removal: {
    name: '毒点清除',
    description: '移除送女、绿帽等毒点情节',
    rules: `请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`
  },

  pacing_improvement: {
    name: '节奏优化',
    description: '优化故事节奏，删除拖沓内容',
    rules: `请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`
  },

  custom: {
    name: '自定义规则',
    description: '用户自定义的改写规则',
    rules: ''
  }
};

// 添加自定义预设规则
export function addCustomPreset(name: string, description: string, rules: string): string {
  const key = `custom_${Date.now()}`;
  PRESET_RULES = {
    ...PRESET_RULES,
    [key]: {
      name,
      description,
      rules
    }
  };
  return key;
}
