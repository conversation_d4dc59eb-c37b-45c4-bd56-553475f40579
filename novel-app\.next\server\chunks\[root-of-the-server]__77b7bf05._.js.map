{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成\nconst GEMINI_API_KEY = 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc';\n// const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber } = request;\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `章节标题：${chapterTitle}` : ''}\n${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  try {\n    const prompt = buildPrompt(request);\n\n    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.7,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192,\n        },\n        safetySettings: [\n          {\n            category: \"HARM_CATEGORY_HARASSMENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_HATE_SPEECH\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          }\n        ]\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.text();\n      console.error('Gemini API error:', errorData);\n      return {\n        rewrittenText: '',\n        success: false,\n        error: `API请求失败: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    const data = await response.json();\n\n    if (!data.candidates || data.candidates.length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '没有收到有效的响应内容',\n      };\n    }\n\n    const candidate = data.candidates[0];\n\n    if (candidate.finishReason === 'SAFETY') {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n      };\n    }\n\n    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '响应内容格式错误',\n      };\n    }\n\n    const rewrittenText = candidate.content.parts[0].text;\n\n    return {\n      rewrittenText: rewrittenText.trim(),\n      success: true,\n    };\n\n  } catch (error) {\n    console.error('Gemini API调用错误:', error);\n    return {\n      rewrittenText: '',\n      success: false,\n      error: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 批量改写多个章节（支持并发）\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number) => void,\n  concurrency: number = 10\n): Promise<Array<{ success: boolean; content: string; error?: string }>> {\n  const results: Array<{ success: boolean; content: string; error?: string }> = new Array(chapters.length);\n  let completed = 0;\n\n  // 分批处理，每批最多 concurrency 个章节\n  const batches: Array<Array<{ content: string; title: string; number: number; index: number }>> = [];\n\n  for (let i = 0; i < chapters.length; i += concurrency) {\n    const batch = chapters.slice(i, i + concurrency).map((chapter, batchIndex) => ({\n      ...chapter,\n      index: i + batchIndex\n    }));\n    batches.push(batch);\n  }\n\n  for (const batch of batches) {\n    // 并发处理当前批次\n    const batchPromises = batch.map(async (chapter) => {\n      try {\n        const result = await rewriteText({\n          originalText: chapter.content,\n          rules,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n        });\n\n        results[chapter.index] = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n        };\n\n        completed++;\n\n        // 更新进度\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return result;\n      } catch (error) {\n        results[chapter.index] = {\n          success: false,\n          content: '',\n          error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        };\n\n        completed++;\n\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return null;\n      }\n    });\n\n    // 等待当前批次完成\n    await Promise.all(batchPromises);\n\n    // 批次间添加短暂延迟\n    if (batches.indexOf(batch) < batches.length - 1) {\n      await new Promise(resolve => setTimeout(resolve, 500));\n    }\n  }\n\n  return results;\n}\n\n// 测试API连接\nexport async function testGeminiConnection(): Promise<{ success: boolean; error?: string }> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 添加自定义预设规则\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;;;;;;AAChB,MAAM,iBAAiB;AACvB,yHAAyH;AACzH,MAAM,iBAAiB;AAevB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,OAAO,CAAC;;;AAGV,EAAE,MAAM;;AAER,EAAE,eAAe,CAAC,KAAK,EAAE,cAAc,GAAG,GAAG;AAC7C,EAAE,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,GAAG,GAAG;;;AAGjD,EAAE,aAAa;;;;;;;;wBAQS,CAAC;AACzB;AAGO,eAAe,YAAY,OAAuB;IACvD,IAAI;QACF,MAAM,SAAS,YAAY;QAE3B,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,KAAK,EAAE,gBAAgB,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU;oBAAC;wBACT,OAAO;4BAAC;gCACN,MAAM;4BACR;yBAAE;oBACJ;iBAAE;gBACF,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,iBAAiB;gBACnB;gBACA,gBAAgB;oBACd;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;iBACD;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC7D;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;QAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;YACvC,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YAC1F,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QAErD,OAAO;YACL,eAAe,cAAc,IAAI;YACjC,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;YACL,eAAe;YACf,SAAS;YACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACnE;IACF;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA+D,EAC/D,cAAsB,EAAE;IAExB,MAAM,UAAwE,IAAI,MAAM,SAAS,MAAM;IACvG,IAAI,YAAY;IAEhB,4BAA4B;IAC5B,MAAM,UAA2F,EAAE;IAEnG,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,YAAa;QACrD,MAAM,QAAQ,SAAS,KAAK,CAAC,GAAG,IAAI,aAAa,GAAG,CAAC,CAAC,SAAS,aAAe,CAAC;gBAC7E,GAAG,OAAO;gBACV,OAAO,IAAI;YACb,CAAC;QACD,QAAQ,IAAI,CAAC;IACf;IAEA,KAAK,MAAM,SAAS,QAAS;QAC3B,WAAW;QACX,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;YACrC,IAAI;gBACF,MAAM,SAAS,MAAM,YAAY;oBAC/B,cAAc,QAAQ,OAAO;oBAC7B;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;gBAC/B;gBAEA,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;gBACrB;gBAEA;gBAEA,OAAO;gBACP,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS;oBACT,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACnE;gBAEA;gBAEA,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT;QACF;QAEA,WAAW;QACX,MAAM,QAAQ,GAAG,CAAC;QAElB,YAAY;QACZ,IAAI,QAAQ,OAAO,CAAC,SAAS,QAAQ,MAAM,GAAG,GAAG;YAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,OAAO;AACT;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;QACzB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACrE;IACF;AACF;AAGO,IAAI,eAAe;IACxB,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;qBAIS,CAAC;IACpB;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;oBAIQ,CAAC;IACnB;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;kBAIM,CAAC;IACjB;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;eAIG,CAAC;IACd;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;IAClC,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/api/presets/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { addCustomPreset } from '@/lib/gemini';\n\n// POST - 保存自定义预设\nexport async function POST(request: NextRequest) {\n  try {\n    const { name, description, rules } = await request.json();\n\n    if (!name || !rules) {\n      return NextResponse.json(\n        { success: false, error: '名称和规则不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 添加自定义预设\n    const presetKey = addCustomPreset(name, description || '', rules);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        presetKey,\n        message: '预设保存成功',\n      },\n    });\n\n  } catch (error) {\n    console.error('保存预设失败:', error);\n    return NextResponse.json(\n      { success: false, error: '保存预设失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvD,IAAI,CAAC,QAAQ,CAAC,OAAO;YACnB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,MAAM,YAAY,IAAA,yIAAe,EAAC,MAAM,eAAe,IAAI;QAE3D,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}